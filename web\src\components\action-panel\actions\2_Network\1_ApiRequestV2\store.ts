import { cloneDeep } from 'lodash-es';
import { defineStore } from 'pinia';

import { FlowData } from '@/components/action-panel/model';
import { useActionFlowStore } from '@/components/action-panel/store/index';

import { ArgsInfoV2 } from './model';

const initialState = (state) => {
  const actionFlowStore = useActionFlowStore();

  const currentArgs = actionFlowStore.currentStep.args || {};
  const isNewAction = !currentArgs.name; // 判断是否为新增动作

  state.args = cloneDeep({
    ...({
      name: actionFlowStore.currentStep.name || '',
      apiId: '',
      description: '',
      headers: [] as FlowData[],
      params: [] as FlowData[],
      body: [] as FlowData[],
      outputs: [] as FlowData[],
      version: 'v2' as const,
      useRoot: isNewAction ? true : (currentArgs.useRoot ?? false), // 新增动作默认true，历史配置默认false
    } as ArgsInfoV2),
    ...currentArgs,
  }) as ArgsInfoV2;
};

export const useApiRequestV2Store = defineStore('ApiRequestV2', {
  state: () => {
    const state = { args: null } as { args: ArgsInfoV2 };
    initialState(state);
    return state;
  },
  getters: {
    variables() {
      return [
        {
          id: 'headers',
          key: 'headers',
          description: '请求头',
          type: 'object',
          children: this.args.headers || [],
        },
        {
          id: 'params',
          key: 'params',
          description: '请求参数',
          type: 'object',
          children: this.args.params || [],
        },
        {
          id: 'body',
          key: 'body',
          description: '请求体',
          type: 'object',
          children: this.args.body || [],
        },
        {
          id: 'result',
          key: 'result',
          description: '响应结果',
          type: 'object',
          value: {
            type: 'variable',
            variableType: 'current',
            variableValue: 'result',
          },
          // 通过API配置加载子节点
          children: this.args.outputs || [],
        },
      ] as FlowData[];
    },
  },
  actions: {
    updateState() {
      initialState(this);
    },
    setArgs(args: ArgsInfoV2) {
      const actionFlowStore = useActionFlowStore();
      actionFlowStore.setCurrentName(args.name);
      actionFlowStore.setCurrentArgs(args);

      // 设置输出参数
      if (args.useRoot) {
        // 使用根节点，直接绑定到根节点
        const resultNode = {
          id: 'result',
          key: 'result',
          description: '请求结果',
          type: 'object',
          children: args.outputs || [],
          value: {
            type: 'variable' as const,
            variableType: 'current' as const,
            variableValue: 'result',
          },
        };
        actionFlowStore.setStepResultData([resultNode]);
      } else {
        // 不使用根节点，使用输出配置
        actionFlowStore.setStepResultData(args.outputs || []);
      }
    },

    // 从API模板加载配置
    loadFromApiTemplate(template: any) {
      if (template.headers) {
        this.args.headers = cloneDeep(template.headers);
      }
      if (template.params) {
        this.args.params = cloneDeep(template.params);
      }
      if (template.body) {
        this.args.body = cloneDeep(template.body);
      }
      if (template.response) {
        this.args.outputs = cloneDeep(template.response);
      }
    },

    // 更新result变量的子节点
    updateResultChildren(responseTemplate: FlowData[]) {
      // 直接使用API定义的响应模板，不进行合并
      this.args.outputs = cloneDeep(responseTemplate);

      // 触发响应式更新
      this.args = { ...this.args };
    },
  },
});
