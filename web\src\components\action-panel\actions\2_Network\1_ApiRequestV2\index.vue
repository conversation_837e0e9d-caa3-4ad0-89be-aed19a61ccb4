<template>
  <div>
    <t-form :data="formData" label-align="left" label-width="100px" class="form-container">
      <div class="form-header">
        <t-row :gutter="[32, 24]">
          <t-col :span="12">
            <t-form-item label="名称" prop="name">
              <t-input v-model="formData.name" placeholder="请输入名称" />
            </t-form-item>
          </t-col>
          <t-col :span="12">
            <t-form-item label="请求API" prop="apiId">
              <t-select
                v-model="formData.apiId"
                placeholder="请选择请求API"
                :options="apiOptions"
                @change="onApiChange"
              >
                <template #suffixIcon>
                  <t-button
                    size="small"
                    variant="text"
                    @click.stop="loadAllTemplates"
                    :disabled="!formData.apiId"
                    style="margin-right: 4px"
                  >
                    <refresh-icon />
                  </t-button>
                </template>
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="12">
            <t-form-item label="备注" prop="description">
              <t-textarea v-model="formData.description" placeholder="请输入备注" />
            </t-form-item>
          </t-col>
        </t-row>
      </div>

      <div class="form-body">
        <!-- 请求参数配置 -->
        <action-form-title title="参数" />
        <variable-list v-model:data="formData.params" :on-change="onVariableChange" />

        <!-- 请求头配置 -->
        <action-form-title title="请求头" />
        <variable-list v-model:data="formData.headers" :on-change="onVariableChange" />

        <!-- 请求体配置 -->
        <action-form-title title="请求体" />
        <variable-list v-model:data="formData.body" :on-change="onVariableChange" />

        <!-- 输出配置 -->
        <action-form-title title="输出参数" />
        <variable-list v-model:data="currentStep.result" :show-root-node="formData.useRoot ?? false" />
      </div>
    </t-form>
  </div>
</template>

<script lang="ts">
export default {
  name: 'ApiRequestV2Actions',
};
</script>

<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { onMounted, ref, watch } from 'vue';
import { RefreshIcon } from 'tdesign-icons-vue-next';
import { MessagePlugin } from 'tdesign-vue-next';

import { api, Services } from '@/api/system';
import VariableList from '@/components/action-panel//VariableList.vue';
import ActionFormTitle from '@/components/action-panel/ActionFormTitle.vue';
import { useActionFlowStore } from '@/components/action-panel/store/index';
import { recursionMergeVariable } from '@/components/action-panel/utils';

import { useApiRequestV2Store } from './store';

const actionFlowStore = useActionFlowStore();
const apiRequestV2Store = useApiRequestV2Store();

watch(
  () => actionFlowStore.currentStep?.id,
  () => {
    apiRequestV2Store.updateState();
  },
  {
    immediate: true,
  },
);

const { currentStep } = storeToRefs(actionFlowStore);
const { args: formData } = storeToRefs(apiRequestV2Store);

watch(
  () => formData.value,
  (newValue) => {
    apiRequestV2Store.setArgs(newValue);
  },
  {
    deep: true,
  },
);

const onVariableChange = () => {
  // 变量变化处理
};
const apiOptions = ref([]);
const apiList = ref([]);

onMounted(() => {
  api
    .run(Services.apiGetAll, {
      apiType: 2,
    })
    .then((res) => {
      apiList.value = res;
      apiOptions.value = res.map((item: any) => ({ label: `${item.apiName}`, value: item.id }));
    });
});

// API选择变化时的处理
const onApiChange = (apiId: string) => {
  const selectedApi = apiList.value.find((api) => api.id === apiId);
  if (selectedApi) {
    loadApiTemplates(selectedApi);
  }
};

// 加载所有模板（从按钮调用）
const loadAllTemplates = () => {
  const selectedApi = apiList.value.find((api) => api.id === formData.value.apiId);
  if (!selectedApi) {
    MessagePlugin.warning('请先选择API');
    return;
  }

  loadApiTemplates(selectedApi);
};

// 统一的API模板加载方法
const loadApiTemplates = async (apiInfo: any) => {
  try {
    // 加载请求头模板 - 使用递归合并，保留已配置的参数和值
    if (apiInfo.headers) {
      const headersTemplate = JSON.parse(apiInfo.headers);
      if (Array.isArray(headersTemplate)) {
        if (!formData.value.headers) {
          formData.value.headers = [];
        }
        recursionMergeVariable(formData.value.headers, headersTemplate);
      }
    }

    // 加载请求参数模板 - 使用递归合并，保留已配置的参数和值
    if (apiInfo.queryParameters) {
      const paramsTemplate = JSON.parse(apiInfo.queryParameters);
      if (Array.isArray(paramsTemplate)) {
        if (!formData.value.params) {
          formData.value.params = [];
        }
        recursionMergeVariable(formData.value.params, paramsTemplate);
      }
    }

    // 加载请求体模板 - 使用递归合并，保留已配置的参数和值
    if (apiInfo.body) {
      const bodyTemplate = JSON.parse(apiInfo.body);
      if (Array.isArray(bodyTemplate)) {
        if (!formData.value.body) {
          formData.value.body = [];
        }
        recursionMergeVariable(formData.value.body, bodyTemplate);
      }
    }

    // 设置响应体配置ID
    if (apiInfo.responseId) {
      formData.value.responseId = apiInfo.responseId;

      // 通过responseId从后端获取响应体模板
      await loadResponseTemplate(apiInfo.responseId);
    } else {
      // 如果没有responseId，清空响应模板
      formData.value.outputs = [];
      apiRequestV2Store.setArgs(formData.value);
    }

    MessagePlugin.success('API模板加载成功');
  } catch (error) {
    console.error('加载API模板失败:', error);
    MessagePlugin.error('加载API模板失败');
  }
};

// 通过responseId加载响应体模板
const loadResponseTemplate = async (responseId: string) => {
  try {
    const responseConfig = await api.run(Services.apiGetResponseById, { id: responseId });
    if (responseConfig && responseConfig.responseData) {
      const responseTemplate = JSON.parse(responseConfig.responseData);
      if (Array.isArray(responseTemplate)) {
        // 对于响应模板，直接使用API定义的结构，不进行合并
        formData.value.outputs = responseTemplate;

        // 触发setArgs来同步更新currentStep.result
        apiRequestV2Store.setArgs(formData.value);
      }
    }
  } catch (error) {
    console.error('加载响应体模板失败:', error);
    MessagePlugin.warning('加载响应体模板失败');
  }
};
</script>

<style lang="less" scoped>
@import '@/style/form.less';
@import '@/style/table.less';

.form-header {
  width: 100%;
  padding: 16px;
}

.config-section {
  margin-bottom: 32px;
  padding: 16px;
  border: 1px solid var(--td-border-level-1-color);
  border-radius: 6px;
  background-color: var(--td-bg-color-container);

  &:last-child {
    margin-bottom: 0;
  }
}

.config-section .variable-list {
  margin-top: 12px;
}
</style>
